<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $title; ?></title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="stylesheet" href="<?php echo base_url(); ?>backend/bootstrap/css/bootstrap.min.css">
    <style type="text/css">
        @media print {
            .no-print { display: none !important; }
            .print-area { margin: 0; padding: 0; }
            body { margin: 0; padding: 0; }
            .page-break { page-break-after: always; }
            * { -webkit-print-color-adjust: exact !important; color-adjust: exact !important; }
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 10px;
        }
        
        .print-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
        }
        
        .barcode-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-content: flex-start;
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
        }
        
        .barcode-item {
            width: 18%;
            margin-bottom: 15px;
            text-align: center;
            padding: 1px 0px 3px 0px;
            box-sizing: border-box;
            background-color: white;
            border: 1px solid #000;
        }

        .book-location {
            font-size: 9px;
            font-weight: bold;
            color: #333;
            padding: 1px 3px 1px 3px;
            margin-bottom: 2px;
            line-height: 1.1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            height: auto;
        }

        .book-author {
            font-size: 8px;
            font-weight: bold;
            color: #333;
            padding: 0px 3px 1px 3px;
            margin-bottom: 0px;
            line-height: 1.1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            height: auto;
        }

        .barcode-image {
            width: 100%;
            height: 40px;
            background-color: white;
            margin-bottom: 0px;
            margin-top: -2px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }
        
        .barcode-number {
            font-size: 12px;
            font-weight: bold;
            color: #000;
            margin-top: 0px;
        }
        
        .control-buttons {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
        
        .btn {
            margin-left: 5px;
        }
        
        /* Code 39 barcode using HTML divs - guaranteed to print and scan */
        .simple-barcode {
            display: flex;
            height: 30px;
            align-items: end;
            justify-content: center;
            width: 100%;
        }

        .bar {
            background-color: #000 !important;
            width: 2px;
            height: 26px;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .space {
            width: 1px;
            height: 26px;
        }
        
        .page-info {
            text-align: center;
            margin: 20px 0;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="control-buttons no-print">
        <button onclick="window.print()" class="btn btn-primary">
            <i class="fa fa-print"></i> Print
        </button>
        <button onclick="generatePDF()" class="btn btn-success">
            <i class="fa fa-file-pdf-o"></i> Download PDF
        </button>
        <button onclick="window.history.back()" class="btn btn-default">
            <i class="fa fa-arrow-left"></i> Back
        </button>
    </div>

    <div class="print-area">
        <div class="print-header no-print">
            <h3><?php echo $title; ?></h3>
            <p>Total Barcodes: <?php echo count($barcodes); ?> | Generated on: <?php echo date('Y-m-d H:i:s'); ?></p>
            <?php if (isset($type) && $type == 'books'): ?>
                <p><strong>Book Barcodes:</strong> Generated from selected library books</p>
            <?php endif; ?>
        </div>

        <?php 
        $barcodes_per_page = 50;
        $total_barcodes = count($barcodes);
        $total_pages = ceil($total_barcodes / $barcodes_per_page);
        
        for ($page = 0; $page < $total_pages; $page++) {
            $start_index = $page * $barcodes_per_page;
            $end_index = min($start_index + $barcodes_per_page, $total_barcodes);
            $page_barcodes = array_slice($barcodes, $start_index, $barcodes_per_page);
        ?>
            
            <?php if ($page > 0): ?>
                <div class="page-break"></div>
            <?php endif; ?>
            
            <div class="page-info no-print">
                Page <?php echo $page + 1; ?> of <?php echo $total_pages; ?>
            </div>
            
            <div class="barcode-container">
                <?php
                foreach ($page_barcodes as $index => $barcode):
                    $book_info = null;
                    if (isset($books) && isset($type) && $type == 'books') {
                        // Find corresponding book info
                        foreach ($books as $book) {
                            if ($book->book_no == $barcode) {
                                $book_info = $book;
                                break;
                            }
                        }
                    }
                ?>
                    <div class="barcode-item">
                        <?php if ($book_info): ?>
                            <div class="book-location">
                                <?php echo htmlspecialchars($book_info->shelving_location ? $book_info->shelving_location : 'N/A'); ?>
                            </div>
                            <div class="book-author">
                                <?php echo htmlspecialchars($book_info->author ? $book_info->author : 'N/A'); ?>
                            </div>
                        <?php endif; ?>
                        <div class="barcode-image">
                            <?php echo generateSimpleBarcode($barcode); ?>
                        </div>
                        <div class="barcode-number">
                            <?php echo $barcode; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
        <?php } ?>
    </div>

    <script src="<?php echo base_url(); ?>backend/jquery/jquery-2.2.3.min.js"></script>
    <script src="<?php echo base_url(); ?>backend/bootstrap/js/bootstrap.min.js"></script>
    
    <script type="text/javascript">
        function generatePDF() {
            var form = document.createElement('form');
            form.method = 'POST';
            form.target = '_blank';

            <?php if (isset($type) && $type == 'books'): ?>
                // Book barcodes PDF
                form.action = '<?php echo base_url(); ?>admin/library_barcode_generator/generateBookPDF';

                <?php foreach ($barcodes as $barcode): ?>
                var bookInput = document.createElement('input');
                bookInput.type = 'hidden';
                bookInput.name = 'book_numbers[]';
                bookInput.value = '<?php echo $barcode; ?>';
                form.appendChild(bookInput);
                <?php endforeach; ?>
            <?php else: ?>
                // Number series PDF
                form.action = '<?php echo base_url(); ?>admin/library_barcode_generator/generatePDF';

                var startInput = document.createElement('input');
                startInput.type = 'hidden';
                startInput.name = 'start_series';
                startInput.value = '<?php echo isset($start_series) ? $start_series : ''; ?>';
                form.appendChild(startInput);

                var endInput = document.createElement('input');
                endInput.type = 'hidden';
                endInput.name = 'end_series';
                endInput.value = '<?php echo isset($end_series) ? $end_series : ''; ?>';
                form.appendChild(endInput);
            <?php endif; ?>

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }
    </script>
</body>
</html>

<?php
// Generate proper Code 39 barcode using divs - guaranteed to print and scan
function generateSimpleBarcode($code) {
    $codeStr = strtoupper($code); // Don't pad with zeros
    $html = '<div class="simple-barcode">';

    // Code 39 character patterns (narrow=1, wide=3)
    $patterns = array(
        '0' => '101001101101',
        '1' => '110100101011',
        '2' => '101100101011',
        '3' => '110110010101',
        '4' => '101001101011',
        '5' => '110100110101',
        '6' => '101100110101',
        '7' => '101001011011',
        '8' => '110100101101',
        '9' => '101100101101',
        'A' => '110101001011',
        'B' => '101101001011',
        'C' => '110110100101',
        'D' => '101011001011',
        'E' => '110101100101',
        'F' => '101101100101',
        '*' => '100101101101' // Start/Stop character
    );

    // Add leading spaces to center and fill width
    $html .= '<div class="space"></div><div class="space"></div>';

    // Start character
    $startPattern = $patterns['*'];
    for ($i = 0; $i < strlen($startPattern); $i++) {
        if ($startPattern[$i] == '1') {
            $html .= '<div class="bar"></div>';
        } else {
            $html .= '<div class="space"></div>';
        }
    }

    // Inter-character gap
    $html .= '<div class="space"></div>';

    // Data characters
    for ($i = 0; $i < strlen($codeStr); $i++) {
        $char = $codeStr[$i];
        if (isset($patterns[$char])) {
            $pattern = $patterns[$char];
            for ($j = 0; $j < strlen($pattern); $j++) {
                if ($pattern[$j] == '1') {
                    $html .= '<div class="bar"></div>';
                } else {
                    $html .= '<div class="space"></div>';
                }
            }
            // Inter-character gap
            $html .= '<div class="space"></div>';
        }
    }

    // Stop character
    $stopPattern = $patterns['*'];
    for ($i = 0; $i < strlen($stopPattern); $i++) {
        if ($stopPattern[$i] == '1') {
            $html .= '<div class="bar"></div>';
        } else {
            $html .= '<div class="space"></div>';
        }
    }

    // Add trailing spaces to fill width
    $html .= '<div class="space"></div><div class="space"></div>';

    $html .= '</div>';
    return $html;
}
?>
