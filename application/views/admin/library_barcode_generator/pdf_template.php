<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style type="text/css">
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 10mm;
        }
        
        .barcode-container {
            width: 100%;
            display: table;
        }
        
        .barcode-row {
            display: table-row;
            margin-bottom: 5mm;
        }
        
        .barcode-item {
            display: table-cell;
            width: 18%;
            text-align: center;
            padding: 0.5mm 0mm 1.5mm 0mm;
            margin: 1mm;
            vertical-align: top;
            background-color: white;
            border: 1px solid #000;
        }
        
        .barcode-image {
            width: 100%;
            height: 12mm;
            margin-bottom: 0mm;
            margin-top: -0.5mm;
            text-align: center;
        }
        
        .book-location {
            font-size: 7pt;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin-bottom: 0.5mm;
            padding: 0.2mm 1mm 0.2mm 1mm;
            line-height: 1.1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            height: auto;
        }

        .book-author {
            font-size: 6pt;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin-bottom: 0mm;
            padding: 0mm 1mm 0.2mm 1mm;
            line-height: 1.1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            height: auto;
        }

        .barcode-number {
            font-size: 10pt;
            font-weight: bold;
            color: #333;
            text-align: center;
        }
        
        .page-break {
            page-break-after: always;
        }
        
        /* Create barcode using HTML/CSS for PDF */
        .barcode-svg {
            width: 100%;
            height: 12mm;
        }
    </style>
</head>
<body>
    <?php 
    $barcodes_per_page = 50;
    $barcodes_per_row = 5;
    $total_barcodes = count($barcodes);
    $total_pages = ceil($total_barcodes / $barcodes_per_page);
    
    for ($page = 0; $page < $total_pages; $page++) {
        $start_index = $page * $barcodes_per_page;
        $end_index = min($start_index + $barcodes_per_page, $total_barcodes);
        $page_barcodes = array_slice($barcodes, $start_index, $barcodes_per_page);
        
        if ($page > 0) {
            echo '<div class="page-break"></div>';
        }
    ?>
        
        <div class="barcode-container">
            <?php 
            $rows = array_chunk($page_barcodes, $barcodes_per_row);
            foreach ($rows as $row): 
            ?>
                <div class="barcode-row">
                    <?php foreach ($row as $barcode):
                        // Find corresponding book info
                        $book_info = null;
                        if (isset($books)) {
                            foreach ($books as $book) {
                                if ($book->book_no == $barcode) {
                                    $book_info = $book;
                                    break;
                                }
                            }
                        }
                    ?>
                        <div class="barcode-item">
                            <?php if ($book_info): ?>
                                <div class="book-location">
                                    <?php echo htmlspecialchars($book_info->shelving_location ? $book_info->shelving_location : 'N/A'); ?>
                                </div>
                                <div class="book-author">
                                    <?php echo htmlspecialchars($book_info->author ? $book_info->author : 'N/A'); ?>
                                </div>
                            <?php endif; ?>
                            <div class="barcode-image">
                                <?php echo generateBarcodeHTML($barcode); ?>
                            </div>
                            <div class="barcode-number"><?php echo $barcode; ?></div>
                        </div>
                    <?php endforeach; ?>
                    
                    <?php 
                    // Fill remaining cells in the row if needed
                    $remaining_cells = $barcodes_per_row - count($row);
                    for ($i = 0; $i < $remaining_cells; $i++): 
                    ?>
                        <div class="barcode-item" style="border: none;"></div>
                    <?php endfor; ?>
                </div>
            <?php endforeach; ?>
        </div>
        
    <?php } ?>
</body>
</html>

<?php
// Helper function to generate proper Code 39 barcode for PDF
function generateBarcodeHTML($code) {
    $codeStr = strtoupper($code); // Don't pad with zeros

    // Code 39 character patterns
    $patterns = array(
        '0' => '101001101101', '1' => '110100101011', '2' => '101100101011',
        '3' => '110110010101', '4' => '101001101011', '5' => '110100110101',
        '6' => '101100110101', '7' => '101001011011', '8' => '110100101101',
        '9' => '101100101101', 'A' => '110101001011', 'B' => '101101001011',
        'C' => '110110100101', 'D' => '101011001011', 'E' => '110101100101',
        'F' => '101101100101', '*' => '100101101101'
    );

    $svg = '<svg class="barcode-svg" viewBox="0 0 120 20" xmlns="http://www.w3.org/2000/svg">';
    $x = 2;

    // Start character
    $startPattern = $patterns['*'];
    for ($i = 0; $i < strlen($startPattern); $i++) {
        if ($startPattern[$i] == '1') {
            $svg .= '<rect x="' . $x . '" y="2" width="1" height="16" fill="black"/>';
        }
        $x += 1;
    }
    $x += 1; // Inter-character gap

    // Data characters
    for ($i = 0; $i < strlen($codeStr); $i++) {
        $char = $codeStr[$i];
        if (isset($patterns[$char])) {
            $pattern = $patterns[$char];
            for ($j = 0; $j < strlen($pattern); $j++) {
                if ($pattern[$j] == '1') {
                    $svg .= '<rect x="' . $x . '" y="2" width="1" height="16" fill="black"/>';
                }
                $x += 1;
            }
            $x += 1; // Inter-character gap
        }
    }

    // Stop character
    $stopPattern = $patterns['*'];
    for ($i = 0; $i < strlen($stopPattern); $i++) {
        if ($stopPattern[$i] == '1') {
            $svg .= '<rect x="' . $x . '" y="2" width="1" height="16" fill="black"/>';
        }
        $x += 1;
    }

    $svg .= '</svg>';
    return $svg;
}
?>
