<div class="content-wrapper">
    <section class="content pb0">
    	<div class="row">
    		<div class="col-lg-6 col-md-6 col-sm-12">
	    		<div class="box box-primary borderwhite">
	                <div class="box-body">
	                	<div class="row">
	                		<div class="col-lg-3 col-md-3 col-sm-3">
								<?php
									// Enhanced image fallback logic with file existence checks
									if (!empty($student_data["image"]) && file_exists('./' . $student_data["image"])) {
                                        $file = base_url() . $student_data["image"] . img_time();
                                    } else {
                                        // Use gender-specific default images
                                        if ($student_data['gender'] == 'Female') {
                                            $default_image = "uploads/student_images/default_female.jpg";
                                        } else {
                                            $default_image = "uploads/student_images/default_male.jpg";
                                        }

                                        // Check if default image exists, otherwise use generic no_image
                                        if (file_exists('./' . $default_image)) {
                                            $file = base_url() . $default_image . img_time();
                                        } else {
                                            $file = base_url() . "uploads/student_images/no_image.png" . img_time();
                                        }
                                    }
								?>

	                			<img src="<?php echo $file; ?>" class="img-rounded img-responsive img-h-150 mb-xs-1" alt="Student profile picture" onerror="this.src='<?php echo base_url(); ?>uploads/student_images/no_image.png'">
								
	                		</div><!--./col-lg-3-->
	                		<div class="col-lg-9 col-md-9 col-sm-9">
	                			<h4 class="mt0"><?php echo $this->lang->line('welcome'); ?>, <?php echo $studentsession_username; ?></h4>
	                			
	                			<!-- Single Combined Attendance Table -->
	                			<div class="table-responsive">
	                				<h5><?php echo $this->lang->line('attendance_overview'); ?></h5>
	                				<table class="table table-bordered table-hover attendance-overview">
	                					<thead>
	                						<tr>
	                							<th class="text-center"><?php echo $this->lang->line('present'); ?></th>
	                							<th class="text-center"><?php echo $this->lang->line('absent'); ?></th>
	                							<th class="text-center"><?php echo $this->lang->line('pod'); ?></th>
	                							<th class="text-center"><?php echo $this->lang->line('total'); ?></th>
	                							<th class="text-center"><?php echo $this->lang->line('percentage'); ?></th>
	                						</tr>
	                					</thead>
	                					<tbody>
	                						<?php
	                						// Calculate Combined Attendance
	                						$total_present = $total_absent = $total_pod = 0;

	                						// Add Subject Attendance
	                						foreach ($attendance_data['subject_attendance'] as $record) {
	                							switch ($record['attendence_type_id']) {
	                								case 1: $total_present += $record['count']; break;
	                								case 4: $total_absent += $record['count']; break;
	                								case 6: $total_pod += $record['count']; break;
	                							}
	                						}

	                						// Add Extra Attendance
	                						foreach ($attendance_data['extra_attendance'] as $record) {
	                							switch ($record['attendence_type_id']) {
	                								case 1: $total_present += $record['count']; break;
	                								case 4: $total_absent += $record['count']; break;
	                								case 6: $total_pod += $record['count']; break;
	                							}
	                						}

	                						// Add Alternate Attendance
	                						foreach ($attendance_data['alternate_attendance'] as $record) {
	                							switch ($record['attendence_type_id']) {
	                								case 1: $total_present += $record['count']; break;
	                								case 4: $total_absent += $record['count']; break;
	                								case 6: $total_pod += $record['count']; break;
	                							}
	                						}

	                						$total_classes = $total_present + $total_absent + $total_pod;
	                						$total_percentage = $total_classes > 0 ? 
	                							round((($total_present + $total_pod) / $total_classes) * 100, 2) : 0;
	                							?>
	                						<tr>
	                							<td class="text-center"><?php echo $total_present; ?></td>
	                							<td class="text-center"><?php echo $total_absent; ?></td>
	                							<td class="text-center"><?php echo $total_pod; ?></td>
	                							<td class="text-center"><?php echo $total_classes; ?></td>
	                							<td class="text-center"><?php echo $total_percentage; ?>%</td>
	                						</tr>
	                					</tbody>
	                				</table>
	                			</div>

	                			<?php if($total_percentage > 0 && $total_percentage < $low_attendance_limit && $low_attendance_limit != '0.00' ){ ?>
	                				<p class="text-danger"><?php echo $this->lang->line('your_current_attendance_is'); ?> <?php echo $total_percentage; ?>% <?php echo $this->lang->line('which_is_lower_than');?> <?php echo $low_attendance_limit; ?>% <?php echo $this->lang->line('of_minimum_attendance_mark'); ?>. </p>				
	                			<?php } elseif($total_percentage > 0 && $total_percentage >= $low_attendance_limit && $low_attendance_limit != '0.00'){ ?>
	                				<p class="text-success"><?php echo $this->lang->line('your_current_attendance_is'); ?> <?php echo $total_percentage; ?>% <?php echo $this->lang->line('which_is_above'); ?> <?php echo $low_attendance_limit; ?>% <?php echo $this->lang->line('of_minimum_attendance_mark'); ?>.</p>	
	                			<?php } ?>

                            <!-- Hostel Application Button -->
                            <?php
                            // Check if hostel fee group is already assigned based on gender and class
                            $student_id = $this->customlib->getStudentSessionUserID();
                            $student_current_class = $this->customlib->getStudentCurrentClsSection();
                            $hostel_fee_assigned = false;
                            $transport_fee_assigned = false;
                            $house_id_disabled = false; // New variable to track house ID 2 restriction

                            if ($student_current_class && $student_id) {
                                $student_details = $this->student_model->get($student_id);
                                if ($student_details && isset($student_current_class->class_id)) {
                                    // Check if student belongs to house ID 2 (disable buttons for house ID 2)
                                    if (isset($student_details['school_house_id']) && $student_details['school_house_id'] == 2) {
                                        $house_id_disabled = true;
                                    }

                                    // Define first year class IDs based on database
                                    $first_year_class_ids = array(
                                        72, // Geography (B.Sc.) Major I
                                        68, // BBA Major (Matigara Campus) I
                                        64, // Zoology Major I
                                        60, // Sociology Major I
                                        56, // Psychology Major I
                                        52, // Political Science Major I
                                        48, // Physics Major I
                                        44, // Microbiology Major I
                                        40, // Mathematics Major I
                                        36, // History Major I
                                        28, // Geography (B.A.) Major I
                                        24, // English Major I
                                        20, // Computer Science Major I
                                        16, // Botany Major I
                                        12, // BCOM Major (Matigara Campus) I
                                        8,  // BCOM Major (Rajganj Campus) I
                                        4   // BCA Major I
                                    );

                                    // Check if student is in first year
                                    $is_first_year = in_array($student_current_class->class_id, $first_year_class_ids);

                                    // Determine fee group based on gender and class year
                                    if ($student_details['gender'] == 'Male') {
                                        if ($is_first_year) {
                                            $hostel_fee_group_id = 45; // Male first year
                                        } else {
                                            $hostel_fee_group_id = 46; // Male other years
                                        }
                                    } elseif ($student_details['gender'] == 'Female') {
                                        if ($is_first_year) {
                                            $hostel_fee_group_id = 53; // Female first year
                                        } else {
                                            $hostel_fee_group_id = 54; // Female other years
                                        }
                                    } else {
                                        $hostel_fee_group_id = 0; // Unknown gender
                                    }

                                    if ($hostel_fee_group_id > 0) {
                                        $hostel_fee_assigned = $this->studentfeemaster_model->checkFeeGroupAssigned($student_current_class->student_session_id, $hostel_fee_group_id);
                                    }

                                    // Check if transport fee is already assigned (fee groups 51 or 52)
                                    $transport_fee_groups = array(51, 52); // Siliguri Route (51), Matigara Bagdogra Route (52)
                                    foreach ($transport_fee_groups as $transport_fee_group_id) {
                                        $existing_transport_assignment = $this->studentfeemaster_model->checkFeeGroupAssigned($student_current_class->student_session_id, $transport_fee_group_id);
                                        if ($existing_transport_assignment) {
                                            $transport_fee_assigned = true;
                                            break;
                                        }
                                    }
                                }
                            }
                            ?>
                            <div class="mt20">
                                <button type="button" class="btn <?php echo ($hostel_fee_assigned || $house_id_disabled) ? 'btn-default' : 'btn-primary'; ?>"
                                        <?php if ($hostel_fee_assigned || $house_id_disabled) { ?>disabled style="background-color: #ccc; color: #666; border-color: #ccc; cursor: not-allowed;"<?php } else { ?>data-toggle="modal" data-target="#hostelApplicationModal"<?php } ?>>
                                    <i class="fa fa-building"></i> <?php echo $this->lang->line('apply_for_hostel_facility'); ?>
                                </button>

                                <button type="button" class="btn <?php echo ($transport_fee_assigned || $house_id_disabled) ? 'btn-default' : 'btn-default'; ?>"
                                        style="margin-left: 10px; <?php echo ($transport_fee_assigned || $house_id_disabled) ? 'background-color: #ccc; color: #666; border-color: #ccc; cursor: not-allowed;' : 'background-color: #333; color: white; border-color: #333;'; ?>"
                                        <?php if ($transport_fee_assigned || $house_id_disabled) { ?>disabled<?php } else { ?>data-toggle="modal" data-target="#transportApplicationModal"<?php } ?>>
                                    <i class="fa fa-bus"></i> Apply for Transport Facility
                                </button>
                            </div>

                            <!-- Hostel Application Modal -->
                            <div class="modal fade" id="hostelApplicationModal" role="dialog">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            <h4 class="modal-title"><?php echo $this->lang->line('hostel_terms_conditions'); ?></h4>
                                        </div>
                                        <div class="modal-body">
                                            <?php 
                                            $student_id = $this->customlib->getStudentSessionUserID();
                                            $student = $this->student_model->get($student_id);
                                            $hostel_name = ($student['gender'] == 'Male') ? 'Xavier Hostel for Men' : 'Patricia Hostel for Women';
                                            ?>
                                            <h4 class="text-center" style="margin-bottom: 30px; color: #031f58; font-weight: 600;"><?php echo $hostel_name; ?></h4>
                                            <div class="row mb20" style="margin-top: 15px;">
                                                <div class="col-md-6">
                                                    <a href="<?php echo ($student['gender'] == 'Male') ? 'https://nbxc.edu.in/wp-content/uploads/2025/06/Xavier-Hostel-for-Men-Fee-Structure.pdf' : 'https://nbxc.edu.in/wp-content/uploads/2025/06/Patricia-Hostel-for-Women.pdf'; ?>" class="btn btn-info btn-block" target="_blank">
                                                        <i class="fa fa-money"></i> <?php echo $this->lang->line('hostel_fee_structure'); ?>
                                                    </a>
                                                </div>
                                                <div class="col-md-6">
                                                    <a href="https://nbxc.edu.in/wp-content/uploads/2025/06/Hostel-Rules-Regulations-NBXC.pdf" class="btn btn-info btn-block" target="_blank">
                                                        <i class="fa fa-list"></i> <?php echo $this->lang->line('hostel_rules_regulations'); ?>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="checkbox" style="margin-top: 20px;">
                                                    <label style="display: block; line-height: 1.5; font-size: 14px;">
                                                        <input type="checkbox" id="hostelTermsCheckbox" style="margin-top: 3px; float: left; margin-right: 5px;"> 
                                                        <span style="display: block; margin-left: 5px;">
                                                            <?php echo $this->lang->line('i_agree_hostel_terms'); ?>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                                            <button type="button" class="btn btn-primary" id="applyHostelBtn" disabled>
                                                <?php echo $this->lang->line('apply'); ?>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Transport Application Modal -->
                            <div class="modal fade" id="transportApplicationModal" tabindex="-1" role="dialog" aria-labelledby="transportApplicationModalLabel">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                            <h4 class="modal-title" id="transportApplicationModalLabel">
                                                <i class="fa fa-bus"></i> Apply for Transport Facility
                                            </h4>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="alert alert-info" style="color: #000000 !important;">
                                                        <i class="fa fa-info-circle"></i>
                                                        <strong>Transport Facility Application</strong><br>
                                                        Please select your transport route and review the transport fee structure and rules before applying.
                                                    </div>

                                                    <div class="text-center" style="margin-bottom: 30px;">
                                                        <a href="https://nbxc.edu.in/wp-content/uploads/2025/06/Transport-Facility-Rules-Fee-Structure-NBXC.pdf"
                                                           target="_blank" class="btn btn-info btn-lg">
                                                            <i class="fa fa-file-pdf-o"></i> Transport Fee & Rules & Regulations
                                                        </a>
                                                    </div>

                                                    <div class="form-group" style="margin-top: 20px;">
                                                        <label><strong>Select Transport Route:</strong></label>
                                                        <div style="margin-top: 15px;">
                                                            <div class="panel-group" id="routeAccordion">
                                                                <?php if (!empty($transport_routes)) {
                                                                    $route_colors = ['text-primary', 'text-success', 'text-info', 'text-warning'];
                                                                    foreach ($transport_routes as $route_index => $route) {
                                                                        $color_class = $route_colors[$route_index % count($route_colors)];
                                                                        $route_id = 'route_' . $route['transport_route_id'];
                                                                        $points_id = 'points_' . $route['transport_route_id'];
                                                                ?>
                                                                <div class="panel panel-default">
                                                                    <div class="panel-heading">
                                                                        <div class="radio">
                                                                            <label>
                                                                                <input type="radio" name="transportRoute" id="<?php echo $route_id; ?>" value="<?php echo $route['transport_route_id']; ?>" required>
                                                                                <i class="fa fa-map-marker <?php echo $color_class; ?>"></i> <strong><?php echo $route['route_title']; ?></strong>
                                                                                <a data-toggle="collapse" data-parent="#routeAccordion" href="#<?php echo $points_id; ?>" class="pull-right" style="margin-left: 8px;">
                                                                                    <span style="font-size: 13px;">View Boarding Points <i class="fa fa-chevron-down"></i></span>
                                                                                </a>
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                    <div id="<?php echo $points_id; ?>" class="panel-collapse collapse">
                                                                        <div class="panel-body" style="padding-top: 15px;">
                                                                            <div class="form-group">
                                                                                <label><strong>Select Pickup Point:</strong></label>
                                                                                <select class="form-control pickup-point-select" name="pickup_point_<?php echo $route['transport_route_id']; ?>" data-route-id="<?php echo $route['transport_route_id']; ?>" required>
                                                                                    <option value="">Select a pickup point</option>
                                                                                    <?php if (!empty($route['point_list'])) {
                                                                                        foreach ($route['point_list'] as $point) { ?>
                                                                                            <option value="<?php echo $point['id']; ?>">
                                                                                                <?php echo $point['pickup_point']; ?>
                                                                                            </option>
                                                                                    <?php }
                                                                                    } ?>
                                                                                </select>
                                                                            </div>

                                                                            <div class="row">
                                                                                <div class="col-md-12">
                                                                                    <h5><strong>Available Pickup Points:</strong></h5>
                                                                                    <div class="pickup-points-list">
                                                                                        <?php if (!empty($route['point_list'])) {
                                                                                            $total_points = count($route['point_list']);
                                                                                            $half_point = ceil($total_points / 2);
                                                                                        ?>
                                                                                        <div class="row">
                                                                                            <div class="col-md-6">
                                                                                                <ul class="list-unstyled">
                                                                                                    <?php for ($i = 0; $i < $half_point; $i++) {
                                                                                                        if (isset($route['point_list'][$i])) { ?>
                                                                                                            <li><i class="fa fa-map-pin text-muted"></i> <?php echo $route['point_list'][$i]['pickup_point']; ?></li>
                                                                                                    <?php }
                                                                                                    } ?>
                                                                                                </ul>
                                                                                            </div>
                                                                                            <div class="col-md-6">
                                                                                                <ul class="list-unstyled">
                                                                                                    <?php for ($i = $half_point; $i < $total_points; $i++) {
                                                                                                        if (isset($route['point_list'][$i])) { ?>
                                                                                                            <li><i class="fa fa-map-pin text-muted"></i> <?php echo $route['point_list'][$i]['pickup_point']; ?></li>
                                                                                                    <?php }
                                                                                                    } ?>
                                                                                                </ul>
                                                                                            </div>
                                                                                        </div>
                                                                                        <?php } ?>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <?php }
                                                                } else { ?>
                                                                <div class="alert alert-warning">
                                                                    <i class="fa fa-exclamation-triangle"></i> <strong>No Transport Routes Available</strong>
                                                                    <br>Transport routes and pickup points have not been configured yet. Please contact the administration to set up transport facilities.
                                                                </div>
                                                                <?php } ?>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label style="font-size: 14px; font-weight: normal;">
                                                            <input type="checkbox" id="transportTermsAccepted" required>
                                                            I have read and agree to the transport facility terms and conditions, rules, and fee structure.
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                                            <button type="button" class="btn btn-success" id="applyTransportBtn" disabled>
                                                <i class="fa fa-check"></i> Apply for Transport Facility
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <style>
                            .pickup-point-select {
                                margin-bottom: 15px;
                            }
                            .pickup-points-list {
                                max-height: 200px;
                                overflow-y: auto;
                                border: 1px solid #ddd;
                                padding: 10px;
                                border-radius: 4px;
                                background-color: #f9f9f9;
                            }
                            .pickup-points-list ul {
                                margin-bottom: 0;
                            }
                            .pickup-points-list li {
                                padding: 2px 0;
                                font-size: 13px;
                            }
                            </style>

                            <script>
                            $(document).ready(function() {
                                // Handle checkbox change
                                $('#hostelTermsCheckbox').change(function() {
                                    $('#applyHostelBtn').prop('disabled', !this.checked);
                                });

                                // Handle apply button click
                                $('#applyHostelBtn').click(function() {
                                    if (!$('#hostelTermsCheckbox').is(':checked')) {
                                        alert('Please accept the terms and conditions first');
                                        return;
                                    }

                                    // Disable button to prevent multiple clicks
                                    var $btn = $(this);
                                    $btn.prop('disabled', true).text('Processing...');

                                    $.ajax({
                                        url: '<?php echo base_url("user/user/assignHostelFeeGroup"); ?>',
                                        type: 'POST',
                                        dataType: 'json',
                                        timeout: 30000, // 30 second timeout
                                        data: {
                                            '<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
                                        },
                                        success: function(response) {
                                            if (response.status == 1) {
                                                alert(response.message);
                                                $('#hostelApplicationModal').modal('hide');
                                                // Redirect to fees page after a short delay
                                                setTimeout(function() {
                                                    window.location.href = '<?php echo base_url("user/user/getfees"); ?>';
                                                }, 1000);
                                            } else {
                                                alert(response.message || 'Failed to assign hostel fee group');
                                                // Re-enable button
                                                $btn.prop('disabled', false).text('<?php echo $this->lang->line('apply'); ?>');
                                            }
                                        },
                                        error: function(xhr, status, error) {
                                            console.error('AJAX Error:', {
                                                status: status,
                                                error: error,
                                                responseText: xhr.responseText,
                                                httpStatus: xhr.status,
                                                url: '<?php echo base_url("user/user/assignHostelFeeGroup"); ?>'
                                            });

                                            var errorMessage = 'An error occurred while processing your request';

                                            if (status === 'parsererror') {
                                                errorMessage = 'Invalid response from server. You may need to log in again.';
                                                console.log('Parse error - likely redirected to login page');
                                            } else if (status === 'timeout') {
                                                errorMessage = 'Request timed out. Please try again.';
                                            } else if (xhr.status === 404) {
                                                errorMessage = 'Service not found. Please contact administrator.';
                                            } else if (xhr.status === 500) {
                                                errorMessage = 'Server error. Please try again later.';
                                            } else if (xhr.status === 403) {
                                                errorMessage = 'Access denied. Please refresh the page and try again.';
                                            } else if (xhr.status === 0) {
                                                errorMessage = 'Network error. Please check your connection.';
                                            }

                                            alert(errorMessage);
                                            // Re-enable button
                                            $btn.prop('disabled', false).text('<?php echo $this->lang->line('apply'); ?>');
                                        }
                                    });
                                });

                                // Transport modal functionality
                                // Handle transport validation
                                function validateTransportForm() {
                                    var routeSelected = $('input[name="transportRoute"]:checked').length > 0;
                                    var pickupPointSelected = false;
                                    var termsAccepted = $('#transportTermsAccepted').is(':checked');

                                    // Check if a pickup point is selected for the selected route
                                    if (routeSelected) {
                                        var selectedRouteId = $('input[name="transportRoute"]:checked').val();
                                        var pickupPointValue = $('select[name="pickup_point_' + selectedRouteId + '"]').val();
                                        pickupPointSelected = pickupPointValue && pickupPointValue !== '';
                                    }

                                    $('#applyTransportBtn').prop('disabled', !(routeSelected && pickupPointSelected && termsAccepted));
                                }

                                // Handle transport route selection change
                                $('input[name="transportRoute"]').change(function() {
                                    // Hide all pickup point selects
                                    $('.pickup-point-select').closest('.form-group').hide();

                                    // Show pickup point select for selected route
                                    var selectedRouteId = $(this).val();
                                    $('select[name="pickup_point_' + selectedRouteId + '"]').closest('.form-group').show();

                                    validateTransportForm();
                                });

                                // Handle pickup point selection change
                                $(document).on('change', '.pickup-point-select', function() {
                                    validateTransportForm();
                                });

                                // Handle transport checkbox change
                                $('#transportTermsAccepted').change(function() {
                                    validateTransportForm();
                                });

                                // Initially hide all pickup point selects
                                $('.pickup-point-select').closest('.form-group').hide();

                                // Handle transport apply button click
                                $('#applyTransportBtn').click(function() {
                                    var selectedRoute = $('input[name="transportRoute"]:checked').val();
                                    var selectedPickupPoint = '';

                                    if (!selectedRoute) {
                                        alert('Please select a transport route first');
                                        return;
                                    }

                                    // Get selected pickup point
                                    selectedPickupPoint = $('select[name="pickup_point_' + selectedRoute + '"]').val();
                                    if (!selectedPickupPoint) {
                                        alert('Please select a pickup point');
                                        return;
                                    }

                                    if (!$('#transportTermsAccepted').is(':checked')) {
                                        alert('Please accept the transport terms and conditions first');
                                        return;
                                    }

                                    // Disable button to prevent multiple clicks
                                    var $btn = $(this);
                                    $btn.prop('disabled', true).text('Processing...');

                                    $.ajax({
                                        url: '<?php echo base_url("user/user/assignTransportFeeGroup"); ?>',
                                        type: 'POST',
                                        dataType: 'json',
                                        timeout: 30000, // 30 second timeout
                                        data: {
                                            'transport_route_id': selectedRoute,
                                            'route_pickup_point_id': selectedPickupPoint,
                                            '<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
                                        },
                                        success: function(response) {
                                            if (response.status == 1) {
                                                alert(response.message);
                                                $('#transportApplicationModal').modal('hide');
                                                // Redirect to fees page after a short delay
                                                setTimeout(function() {
                                                    window.location.href = '<?php echo base_url("user/user/getfees"); ?>';
                                                }, 1000);
                                            } else {
                                                alert(response.message || 'Failed to assign transport fee group');
                                                // Re-enable button
                                                $btn.prop('disabled', false).text('Apply for Transport Facility');
                                            }
                                        },
                                        error: function(xhr, status, error) {
                                            console.error('AJAX Error:', {
                                                status: status,
                                                error: error,
                                                responseText: xhr.responseText,
                                                httpStatus: xhr.status,
                                                url: '<?php echo base_url("user/user/assignTransportFeeGroup"); ?>'
                                            });

                                            var errorMessage = 'An error occurred while processing your request';

                                            if (status === 'parsererror') {
                                                errorMessage = 'Invalid response from server. You may need to log in again.';
                                                console.log('Parse error - likely redirected to login page');
                                            } else if (status === 'timeout') {
                                                errorMessage = 'Request timed out. Please try again.';
                                            } else if (xhr.status === 404) {
                                                errorMessage = 'Service not found. Please contact administrator.';
                                            } else if (xhr.status === 500) {
                                                errorMessage = 'Server error. Please try again later.';
                                            } else if (xhr.status === 403) {
                                                errorMessage = 'Access denied. Please refresh the page and try again.';
                                            } else if (xhr.status === 0) {
                                                errorMessage = 'Network error. Please check your connection.';
                                            }

                                            alert(errorMessage);
                                            // Re-enable button
                                            $btn.prop('disabled', false).text('Apply for Transport Facility');
                                        }
                                    });
                                });
                            });
                            </script>
	                		 
	                		</div><!--./col-lg-9-->
	                	</div><!--./row-->
	                </div>
	            </div>   
	        </div><!--./col-lg-6-->
			<div class="col-lg-6 col-md-6 col-sm-12">	    		 
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('notice_board'); ?></h3>      
                    </div>
                    <div class="box-body pb0">
						<?php if(!empty($notificationlist)){ ?>
                    	<ul class="user-progress ps mb0">
                    		<?php for($i=0;$i<4;$i++){
                    			$notification = array();
                    			if(!empty($notificationlist[$i])){
	                    			$notification=$notificationlist[$i];
                                }
	                    	?>
	                    <?php if(!empty($notification)){ ?>
			                <li class="doc-file-type">			                   
				                <div class="set-flex">
					                <div class="media-title"><?php if(!empty($notification)){ ?>
									<a href="<?php echo base_url(); ?>user/notification" class="displayinline text-muted" target="_blank">
									
					                	<?php if ($notification['notification_id'] == "read") { ?>
                                            <img src="<?php echo base_url() ?>/backend/images/read_one.png">
                                        <?php } else { ?>
                                            <img src="<?php echo base_url() ?>backend/images/unread_two.png">
                                        <?php }?>
										
										&nbsp;<?php  echo $notification['title']; ?> (<?php if(!empty($notification)){ echo "<i class='fa fa-clock-o text-aqua'></i>". ' '. date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($notification['date']));} ?>)
					                </a><?php } ?>
									</div>                

			            		</div>   
				               
			                </li><!-- /.item -->
			            <?php } } ?>
			                
			            </ul>  
						<?php }else{ ?>
							<img src="https://smart-school.in/ssappresource/images/addnewitem.svg"  width="150" class="center-block mt20">
						<?php } ?>
                    </div>                   
                </div>
			</div><!--./col-lg-6-->  
    	</div><!--./row-->	

		<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('fee_details'); ?></h3>      
                    </div>
                    <div class="box-body direct-chat-messages">
						<?php
						$currency_symbol = $this->customlib->getSchoolCurrencyFormat();
						$student_id = $this->customlib->getStudentSessionUserID();
						$student_current_class = $this->customlib->getStudentCurrentClsSection();
						
						// Use the same method as getfees page to fetch student fee details
						$student_due_fee = $this->studentfeemaster_model->getStudentFees($student_current_class->student_session_id);
						
						$total_amount = 0;
						$total_deposite_amount = 0;
						$total_fine_amount = 0;
						$total_discount_amount = 0;
						$total_balance_amount = 0;
						$total_fees_fine_amount = 0;
						
						if(!empty($student_due_fee)) {
						?>
						<div class="table-responsive">
							<table class="table table-striped table-hover">
								<thead>
									<tr class="active">
										<th><?php echo $this->lang->line('fees_group'); ?></th>
										<th class="text text-center"><?php echo $this->lang->line('due_date'); ?></th>
										<th class="text text-right"><?php echo $this->lang->line('amount'); ?> <span><?php echo "(" . $currency_symbol . ")"; ?></span></th>
										<th class="text text-right"><?php echo $this->lang->line('paid'); ?> <span><?php echo "(" . $currency_symbol . ")"; ?></span></th>
										<th class="text text-right"><?php echo $this->lang->line('balance'); ?> <span><?php echo "(" . $currency_symbol . ")"; ?></span></th>
									</tr>
								</thead>
								<tbody>
									<?php
									$pending_fee_count = 0;
									foreach ($student_due_fee as $fee_key => $fee) {
										foreach ($fee->fees as $fee_value) {
											$fee_paid = 0;
											$fee_discount = 0;
											$fee_fine = 0;
											
											if (!empty($fee_value->amount_detail)) {
												$fee_deposits = json_decode(($fee_value->amount_detail));
												foreach ($fee_deposits as $fee_deposits_value) {
													$fee_paid += $fee_deposits_value->amount;
													$fee_discount += $fee_deposits_value->amount_discount;
													$fee_fine += $fee_deposits_value->amount_fine;
												}
											}
											
											$total_amount += $fee_value->amount;
											$total_discount_amount += $fee_discount;
											$total_deposite_amount += $fee_paid;
											$total_fine_amount += $fee_fine;
											$feetype_balance = $fee_value->amount - ($fee_paid + $fee_discount);
											$total_balance_amount += $feetype_balance;
											
											// Only show pending fee entries
											if($feetype_balance > 0) {
												$pending_fee_count++;
												// Limit to showing only 3 entries max
												if($pending_fee_count <= 3) {
												?>
												<tr <?php if ($feetype_balance > 0 && strtotime($fee_value->due_date) < strtotime(date('Y-m-d'))) { echo 'class="danger font12"'; } ?>>
													<td>
														<?php echo $fee_value->name . " (" . $fee_value->type . ")"; ?>
													</td>
													<td class="text text-center">
														<?php
														if ($fee_value->due_date == "0000-00-00") {
															echo "&nbsp;";
														} else {
															echo date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($fee_value->due_date));
														}
														?>
													</td>
													<td class="text text-right">
														<?php 
														echo $fee_value->amount;
														if (($fee_value->due_date != "0000-00-00" && $fee_value->due_date != NULL) && (strtotime($fee_value->due_date) < strtotime(date('Y-m-d')))) {
															echo " + " . ($fee_value->fine_amount);
														}
														?>
													</td>
													<td class="text text-right">
														<?php echo amountFormat($fee_paid, 2, '.', ''); ?>
													</td>
													<td class="text text-right">
														<?php 
														$display_none = "ss-none";
														if ($feetype_balance > 0) {
															$display_none = "";
															echo amountFormat($feetype_balance, 2, '.', ''); 
														} 
														?>
													</td>
												</tr>
												<?php
												}
											}
										}
									}
									
									if($pending_fee_count == 0) {
										echo '<tr><td colspan="5" class="text-center">' . $this->lang->line('no_pending_fees') . '</td></tr>';
									} elseif($pending_fee_count > 3) {
										echo '<tr><td colspan="5" class="text-center">' . ($pending_fee_count - 3) . ' ' . $this->lang->line('more_fees_pending') . '</td></tr>';
									}
									?>
								</tbody>
								<tfoot>
									<tr>
										<td style="font-weight: bold;"></td>
										<td class="text text-center" style="font-weight: bold;"><?php echo $this->lang->line('grand_total'); ?></td>
										<td class="text text-right" style="font-weight: bold;"><?php echo $currency_symbol . amountFormat($total_amount, 2, '.', ''); ?></td>
										<td class="text text-right" style="font-weight: bold;"><?php echo $currency_symbol . amountFormat($total_deposite_amount, 2, '.', ''); ?></td>
										<td class="text text-right" style="font-weight: bold;"><?php echo $currency_symbol . amountFormat($total_balance_amount, 2, '.', ''); ?></td>
									</tr>
								</tfoot>
							</table>
						</div>
						<?php } else { ?>
							<div class="alert alert-info">
								<?php echo $this->lang->line('no_fees_found'); ?>
							</div>
						<?php } ?>
						<div class="box-footer">
							<a href="<?php echo base_url(); ?>user/user/getfees" class="btn btn-primary pull-right btn-sm" style="margin-left: 5px;">
								<i class="fa fa-money"></i> <?php echo $this->lang->line('view_all_fees'); ?>
							</a>
							<a href="<?php echo base_url(); ?>user/user/getfees" class="btn btn-success pull-right btn-sm">
								<i class="fa fa-credit-card"></i> <?php echo $this->lang->line('pay_online'); ?>
							</a>
						</div>
                    </div>
                </div>
			</div><!--./col-lg-6-->
		 
		<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('subject_progress'); ?></h3>      
                    </div>
                    <div class="box-body direct-chat-messages">
                    	<div class="table-responsive">
							<?php   if (!empty($subjects_data)) {  ?>
                    		<table class="table table-striped table-hover">
                    			<tr class="active">
                    				<th><?php echo $this->lang->line('subject'); ?></th>
                    				<th><?php echo $this->lang->line('progress'); ?></th>
                    				<!-- <th>Duration</th> -->
                    			</tr>
                    		<?php 
                                    foreach ($subjects_data as $key => $value) {
                            ?>
                    			<tr>
                    				<td><?php echo $value['lebel']; ?></td>
                    				<td><?php echo $value['complete']; ?>%
                    					<div class="progress progress-minibar">
										  <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow=""
										  aria-valuemin="0" aria-valuemax="100" style="width:<?php if($value['complete'] !=0){ echo $value['complete'];} ?>%">
										  </div>
										</div>
                    				</td>
                    				<!-- <td>2 Months</td> -->
                    			</tr>
                    		<?php }  ?>
                    			
                    		</table>
							<?php }else{  ?>
								<img src="https://smart-school.in/ssappresource/images/addnewitem.svg"  width="150" class="center-block mt20">
							<?php } ?>
                    	</div>
                    </div>
                </div>
			</div><!--./col-lg-4-->

			

			<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('upcomming_class'); ?></h3>      
                    </div>
                    <div class="box-body direct-chat-messages">					 
					
						<?php if (!empty($timetable)) { ?>
                    	<ul class="user-progress">

                    	<?php 
                    		foreach ($timetable as $tm_key => $tm_value) {

                    			if (!$timetable[$tm_key]) {
                    	 ?>
			            <?php }else{ 
                                for($i=0;$i<5;$i++){

	                                $timetablelist = array();
	                    			if(!empty($timetable[$tm_key][$i])){
	                    				
		                    			$timetablelist=$timetable[$tm_key][$i];
		                 
	                                }
 
			             if(!empty($timetablelist)){ ?>
			            	<li class="lecture-list">

			            		<?php
			            		$profile_pic = '';
			            		if($timetablelist->image != '' && file_exists('./uploads/staff_images/' . $timetablelist->image)){
				            	    $profile_pic = 'uploads/staff_images/' . $timetablelist->image;
				            	} else {
				            	    if($timetablelist->gender == 'Male'){
	                                    $default_image = 'uploads/staff_images/default_male.jpg';
				            		} else {
	                                    $default_image = 'uploads/staff_images/default_female.jpg';
				            		}

				            		if (file_exists('./' . $default_image)) {
				            		    $profile_pic = $default_image;
				            		} else {
				            		    $profile_pic = 'uploads/staff_images/no_image.png';
				            		}
			            		}?>
			                    <img src="<?php echo base_url() . $profile_pic . img_time(); ?>" alt="Staff profile picture" class="img-circle msr-3 object-fit-cover fit-image-40" width="40" height="40" onerror="this.src='<?php echo base_url(); ?>uploads/staff_images/no_image.png'">

				                <div class="set-flex">
					                <div class="media-title bmedium"><?php echo $timetablelist->name.' '.$timetablelist->surname.' (' . $timetablelist->employee_id .')'; ?> 
					                </div>
					                <div class="text-muted mb0">
					                	<?php
					                	if(!empty($timetablelist)){
                                            echo $timetablelist->subject_name;
                                            if ($timetablelist->code != '') {
                                                echo " (" . $timetablelist->code . ")";
                                            }
                                            // Optional tag removed as per requirement
                                        }
                                        ?>
                                    </div>
			            		</div>    
				                 <div class="ms-auto">
					                <div class="bmedium"><?php echo $this->lang->line('room_no'); ?>:<?php echo $timetablelist->room_no; ?></div>
					                <div class="text-muted mb0"><?php echo $timetablelist->time_from ?>-<?php echo $timetablelist->time_to; ?></div>
				                 </div>
			                </li>
			           <?php } } } }  ?>
			               
			            </ul>
						<?php }else{  ?>
							<img src="https://smart-school.in/ssappresource/images/addnewitem.svg"  width="150" class="center-block mt20">
						<?php } ?>
                    </div>
                </div>
			</div><!--./col-lg-4-->

			<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('homework'); ?></h3>      
                    </div>

                    <div class="box-body direct-chat-messages">
                    	
						<?php if(!empty($homeworklist)){ ?>
                    	<ul class="user-progress ps">
                    		<?php for($i=0;$i<5;$i++){
                    			$homework = array();
                    			if(!empty($homeworklist[$i])){
	                    			$homework=$homeworklist[$i];
                                }
	                    	?>
	                    <?php if(!empty($homework)){ ?>
			                <li class="doc-file-type">
				                <div class="set-flex">
					                <div class="media-title font-16"><?php if(!empty($homework)){ ?><a href="<?php echo base_url(); ?>user/homework" class="displayinline text-muted" target="_blank"><?php  echo $homework['subject_name']; ?> (<?php  echo $homework['subject_code']; ?>)									
									</a><?php } ?></div>
					                <div class="text-muted mb0"><?php if(!empty($homework)){ echo $this->lang->line('homework_date').': '. date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($homework['homework_date'])) .',';} ?> <?php if(!empty($homework)){ echo $this->lang->line('submission_date'). ': '. date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($homework['submit_date'])) .',';} ?> <?php if(!empty($homework)){

                                        if ($homework["status"] == 'submitted') {
                                            $status_class = "class= 'label label-warning'";
                                            $status_homework = $this->lang->line('submitted');
                                        }else{
                                            $status_class = "class= 'label label-danger'";
                                            $status_homework = $this->lang->line("pending");
                                        }
                      
                                        if ($homework["homework_evaluation_id"] != 0) {
                                           
                                            $status_class = "class= 'label label-success'";
                                            $status_homework = $this->lang->line("evaluated");
                                        }

                                        echo $this->lang->line('status').': ';
                                        ?>
                                        <label <?php echo $status_class; ?>><?php echo $status_homework; ?></label>
								    <?php	
								    }
									?>

					            </div>
			            		</div> 
			                </li><!-- /.item -->
			            <?php } } ?>			                
			            </ul> 
						<?php }else{ ?>
							<img src="https://smart-school.in/ssappresource/images/addnewitem.svg"  width="150" class="center-block mt20">
						<?php } ?>
                    </div>
                </div>
			</div><!--./col-lg-4-->	
			
			
			<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('teacher_list'); ?></h3>      
                    </div>

                    <div class="box-body direct-chat-messages">                    	
						<?php  					 
						 
							if(!empty($teacherlist)){   
								
						?>
                    	<ul class="user-progress ps">
                    		<?php foreach ($teacherlist as $teacher) {							 
								
								$class_teacher = '';
							
								if ($teacher[0]->class_teacher == $teacher[0]->staff_id) {
									$class_teacher = '<span class="label label-success bolds">' . $this->lang->line('class_teacher') . '</span>' ;
								}
							?>
							<li class="lecture-list">

			            		<?php 
										$profile_pic = '';
										
										if($teacher[0]->image !=''){
											$profile_pic = 'uploads/staff_images/'.$teacher[0]->image;
										}else{
											if($teacher[0]->gender == 'Male'){
												$profile_pic = 'uploads/staff_images/default_male.jpg';
											}else{
												$profile_pic = 'uploads/staff_images/default_female.jpg';
											} 
										}
								?>
			                    <img src="<?php echo base_url(); ?><?php echo $profile_pic.img_time(); ?>" alt="" class="img-circle msr-3 object-fit-cover fit-image-40" width="40" height="40">

				                <div class="set-flex">
					                <div class="media-title bmedium"><?php echo $teacher[0]->name . " " . $teacher[0]->surname . "<br> (" . $teacher[0]->employee_id . ") " . $class_teacher ?>	 
					                </div>
					                 
			            		</div>  
			                </li>
							
			                <?php } ?>		            		                
			            </ul> 
						<?php  }else{ ?>
							<img src="https://smart-school.in/ssappresource/images/addnewitem.svg"  width="150" class="center-block mt20">
						<?php } ?>
                    </div>
                </div>
			</div><!--./col-lg-4-->	
		</div><!--./row-->	
	</section>
</div>

<style>
    .table-bordered {
        border: 1px solid #ddd;
    }
    .table-bordered > thead > tr > th,
    .table-bordered > tbody > tr > td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
    }
    .table-bordered > thead > tr > th {
        background-color: #f5f5f5;
        font-weight: bold;
    }
    .table-hover > tbody > tr:hover {
        background-color: #f9f9f9;
    }
    .attendance-overview {
        border-collapse: separate;
        border-spacing: 0;
        border: 1px solid #ddd;
        border-radius: 5px;
        overflow: hidden;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        margin-top: 5px;
    }
    .attendance-overview thead {
        background-color: #f8f9fa;
    }
    .attendance-overview th,
    .attendance-overview td {
        padding: 12px;
        text-align: center;
        border: none;
        border-bottom: 1px solid #ddd;
    }
    .attendance-overview th {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.9em;
        color: #495057;
    }
    .attendance-overview td {
        font-size: 1.1em;
        color: #212529;
    }
    .attendance-overview tbody tr:last-child td {
        border-bottom: none;
    }
    .attendance-overview tbody tr:hover {
        background-color: #f1f3f5;
    }
    .mb10 {
        margin-bottom: 10px;
    }
    h5 {
        margin-bottom: 5px;
        color: #495057;
        font-weight: 600;
    }
    .attendance-subtitle {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
        font-size: 0.9em;
    }
    .mb10 {
        margin-bottom: 15px;
    }
    .attendance-overview .table-total {
        background-color: #f8f9fa;
        font-weight: 600;
    }
    .attendance-overview .table-total td {
        border-top: 2px solid #dee2e6;
    }
</style>

s