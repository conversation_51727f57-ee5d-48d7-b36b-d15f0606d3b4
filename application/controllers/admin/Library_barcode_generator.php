<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Library_barcode_generator extends Admin_Controller {

    function __construct() {
        parent::__construct();
        $this->load->model('book_model');
        $this->load->model('bookissue_model');
        $this->load->model('librarymember_model');
        $this->load->model('staff_model');
        $this->sch_setting_detail = $this->setting_model->getSetting();

        // Try to load borrowing policies model
        try {
            $this->load->model('borrowingpolicies_model');
        } catch (Exception $e) {
            // Borrowing policies not available, will use defaults
        }
    }

    public function index() {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'library/library_barcode_generator');
        
        $data['title'] = 'Barcode Generator';
        $data['title_list'] = 'Generate Barcodes';
        $data['sch_setting'] = $this->sch_setting_detail;
        
        $this->load->view('layout/header', $data);
        $this->load->view('admin/library_barcode_generator/index', $data);
        $this->load->view('layout/footer', $data);
    }

    public function generate() {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $start_series = $this->input->post('start_series');
        $end_series = $this->input->post('end_series');

        // Validate input
        if (empty($start_series) || empty($end_series)) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Please enter both starting and ending barcode series.</div>');
            redirect('admin/library_barcode_generator');
        }

        if (!is_numeric($start_series) || !is_numeric($end_series)) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Please enter numeric values only.</div>');
            redirect('admin/library_barcode_generator');
        }

        if ($start_series > $end_series) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Starting series must be less than or equal to ending series.</div>');
            redirect('admin/library_barcode_generator');
        }

        $total_barcodes = $end_series - $start_series + 1;
        if ($total_barcodes > 1000) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Maximum 1000 barcodes can be generated at once.</div>');
            redirect('admin/library_barcode_generator');
        }

        // Generate barcodes array
        $barcodes = array();
        for ($i = $start_series; $i <= $end_series; $i++) {
            $barcodes[] = $i; // Don't pad with leading zeros
        }

        $data['barcodes'] = $barcodes;
        $data['title'] = 'Barcode Print Preview';
        $data['start_series'] = $start_series;
        $data['end_series'] = $end_series;
        
        $this->load->view('admin/library_barcode_generator/print_preview_simple', $data);
    }

    public function generatePDF() {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $start_series = $this->input->post('start_series');
        $end_series = $this->input->post('end_series');

        // Validate input
        if (empty($start_series) || empty($end_series)) {
            show_error('Invalid barcode series provided.');
        }

        if (!is_numeric($start_series) || !is_numeric($end_series)) {
            show_error('Please enter numeric values only.');
        }

        if ($start_series > $end_series) {
            show_error('Starting series must be less than or equal to ending series.');
        }

        $total_barcodes = $end_series - $start_series + 1;
        if ($total_barcodes > 1000) {
            show_error('Maximum 1000 barcodes can be generated at once.');
        }

        // Generate barcodes array
        $barcodes = array();
        for ($i = $start_series; $i <= $end_series; $i++) {
            $barcodes[] = $i; // Don't pad with leading zeros
        }

        $data['barcodes'] = $barcodes;
        
        // Generate PDF
        $html = $this->load->view('admin/library_barcode_generator/pdf_template', $data, true);
        
        $this->load->library('m_pdf');
        $mpdf = $this->m_pdf->load([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_left' => 10,
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ]);
        
        $mpdf->WriteHTML($html);
        $filename = 'barcodes_' . $start_series . '_to_' . $end_series . '_' . date('Y-m-d_H-i-s') . '.pdf';
        $mpdf->Output($filename, 'D'); // D for download
    }

    public function searchBooks() {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $query = $this->input->post('query');

        if (empty($query) || strlen($query) < 2) {
            echo json_encode([]);
            return;
        }

        // Search books by title, author, or book number
        $this->db->select('id, book_title, book_no, author, shelving_location');
        $this->db->from('books');
        $this->db->group_start();
        $this->db->like('book_title', $query);
        $this->db->or_like('author', $query);
        $this->db->or_like('book_no', $query);
        $this->db->group_end();
        $this->db->limit(20); // Limit results for performance
        $this->db->order_by('book_title', 'ASC');

        $books = $this->db->get()->result();

        echo json_encode($books);
    }

    public function generateBookBarcodes() {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $book_numbers = $this->input->post('book_numbers');

        // Validate input
        if (empty($book_numbers) || !is_array($book_numbers)) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Please select at least one book.</div>');
            redirect('admin/library_barcode_generator');
        }

        if (count($book_numbers) > 100) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Maximum 100 books can be selected at once.</div>');
            redirect('admin/library_barcode_generator');
        }

        // Get book details for the selected book numbers
        $this->db->select('book_no, book_title, author, shelving_location');
        $this->db->from('books');
        $this->db->where_in('book_no', $book_numbers);
        // Remove is_active check for now
        $books = $this->db->get()->result();

        if (empty($books)) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">No valid books found for the selected book numbers.</div>');
            redirect('admin/library_barcode_generator');
        }

        // Prepare data for barcode generation
        $barcodes = array();
        foreach ($books as $book) {
            $barcodes[] = $book->book_no;
        }

        $data['barcodes'] = $barcodes;
        $data['books'] = $books; // Pass book details for display
        $data['title'] = 'Book Barcode Print Preview';
        $data['type'] = 'books'; // Indicate this is for books

        $this->load->view('admin/library_barcode_generator/print_preview_simple', $data);
    }

    public function generateBookPDF() {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $book_numbers = $this->input->post('book_numbers');

        // Validate input
        if (empty($book_numbers) || !is_array($book_numbers)) {
            show_error('No book numbers provided.');
        }

        if (count($book_numbers) > 100) {
            show_error('Maximum 100 books can be selected at once.');
        }

        // Get book details for the selected book numbers
        $this->db->select('book_no, book_title, author, shelving_location');
        $this->db->from('books');
        $this->db->where_in('book_no', $book_numbers);
        // Remove is_active check for now
        $books = $this->db->get()->result();

        if (empty($books)) {
            show_error('No valid books found for the selected book numbers.');
        }

        // Prepare data for PDF generation
        $barcodes = array();
        foreach ($books as $book) {
            $barcodes[] = $book->book_no;
        }

        $data['barcodes'] = $barcodes;
        $data['books'] = $books;

        // Generate PDF
        $html = $this->load->view('admin/library_barcode_generator/pdf_template', $data, true);

        $this->load->library('m_pdf');
        $mpdf = $this->m_pdf->load([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_left' => 10,
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ]);

        $mpdf->WriteHTML($html);
        $filename = 'book_barcodes_' . date('Y-m-d_H-i-s') . '.pdf';
        $mpdf->Output($filename, 'D'); // D for download
    }

    public function generateBookLabels() {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $book_numbers = $this->input->post('book_numbers');

        // Validate input
        if (empty($book_numbers) || !is_array($book_numbers)) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Please select at least one book.</div>');
            redirect('admin/library_barcode_generator');
        }

        if (count($book_numbers) > 100) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Maximum 100 books can be selected at once.</div>');
            redirect('admin/library_barcode_generator');
        }

        // Get book details for the selected book numbers including call number, rack number and subject
        $this->db->select('book_no, book_title, author, shelving_location,
                          IFNULL(NULLIF(call_no, ""), IFNULL(NULLIF(rack_no, ""), book_no)) as call_no,
                          IFNULL(subject, "General") as subject');
        $this->db->from('books');
        $this->db->where_in('book_no', $book_numbers);
        $books = $this->db->get()->result();

        if (empty($books)) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">No valid books found for the selected book numbers.</div>');
            redirect('admin/library_barcode_generator');
        }

        $data['books'] = $books;
        $data['title'] = 'Book Labels Print Preview';
        $data['type'] = 'labels'; // Indicate this is for labels

        $this->load->view('admin/library_barcode_generator/labels_preview', $data);
    }

}
